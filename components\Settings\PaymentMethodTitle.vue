<template>
  <div
    class="flex items-center justify-between py-3 px-4 ml-14 border-l-2 border-blue-200"
  >
    <!-- Left Side: Toggle + Icon + Account Info -->
    <div class="flex items-center gap-3">
      <!-- Account Toggle -->
      <ToggleSwitch
        :model-value="paymentGateway.activated"
        size="sm"
        @change="handleToggle"
      />
      <!-- Account Icon -->
      <div
        class="w-6 h-6 rounded flex items-center justify-center bg-white border border-gray-200"
      >
        <svg
          class="w-3 h-3 text-gray-600"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"
          />
        </svg>
      </div>

      <!-- Payment Gateway Info -->
      <div class="flex-1">
        <div class="flex items-center gap-2 mb-2">
          <span class="text-sm font-medium text-gray-900">
            {{ paymentGateway.name || paymentGateway.id }}
          </span>
          <span
            v-if="paymentGateway.activated"
            class="text-xs text-green-600 bg-green-50 px-2 py-0.5 rounded"
          >
            Đã kích hoạt
          </span>
        </div>

        <!-- Display payment gateway information -->
        <div class="space-y-1">
          <div
            v-if="paymentGateway.partnerCode"
            class="flex items-center gap-2 text-xs text-gray-600"
          >
            <span class="font-medium min-w-[80px]">Partner Code:</span>
            <span class="text-gray-800">{{ paymentGateway.partnerCode }}</span>
          </div>

          <div
            v-if="paymentGateway.subMethodCode"
            class="flex items-center gap-2 text-xs text-gray-600"
          >
            <span class="font-medium min-w-[80px]">Sub Method:</span>
            <span class="text-gray-800">{{
              paymentGateway.subMethodCode
            }}</span>
          </div>

          <div
            v-if="paymentGateway.gwPartnerCode"
            class="flex items-center gap-2 text-xs text-gray-600"
          >
            <span class="font-medium min-w-[80px]">Gateway Code:</span>
            <span class="text-gray-800">{{
              paymentGateway.gwPartnerCode
            }}</span>
          </div>

          <div
            v-if="paymentGateway.gwPartnerName"
            class="flex items-center gap-2 text-xs text-gray-600"
          >
            <span class="font-medium min-w-[80px]">Gateway Name:</span>
            <span class="text-gray-800">{{
              paymentGateway.gwPartnerName
            }}</span>
          </div>

          <div
            v-if="paymentGateway.accessKey"
            class="flex items-center gap-2 text-xs text-gray-600"
          >
            <span class="font-medium min-w-[80px]">Access Key:</span>
            <span class="text-gray-800 font-mono">{{
              maskSensitiveData(paymentGateway.accessKey)
            }}</span>
          </div>

          <div
            v-if="paymentGateway.requestUrl"
            class="flex items-center gap-2 text-xs text-gray-600"
          >
            <span class="font-medium min-w-[80px]">Request URL:</span>
            <span class="text-blue-600 text-xs break-all">{{
              paymentGateway.requestUrl
            }}</span>
          </div>

          <div
            v-if="paymentGateway.description"
            class="flex items-center gap-2 text-xs text-gray-600"
          >
            <span class="font-medium min-w-[80px]">Mô tả:</span>
            <span class="text-gray-800">{{ paymentGateway.description }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Side: Actions -->
    <div class="flex items-center gap-2">
      <button @click="handleEdit" class="p-1 text-gray-400 hover:text-gray-600">
        <svg
          class="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
          />
        </svg>
      </button>

      <button class="p-1 text-gray-400 hover:text-gray-600">
        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path
            d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"
          />
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface PaymentGateway {
  __typename?: string;
  id: string;
  name: string;
  methodCode: string;
  partnerCode: string;
  subMethodCode: string;
  gwPartnerCode: string;
  gwPartnerName?: string;
  gwSubChannel?: string;
  gwMethodVersion?: string;
  hashAlgorithm?: string;
  accessKey: string;
  secretKey?: string;
  requestUrl: string;
  description: string;
  activated: boolean;
}

interface Props {
  paymentGateway: PaymentGateway;
  methodId: string;
}

interface Emits {
  (e: "toggle", titleId: string, value: boolean): void;
  (e: "edit", titleId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Helper functions
const maskSensitiveData = (data: string): string => {
  if (!data || data.length <= 8) return data;
  const visibleStart = data.slice(0, 4);
  const visibleEnd = data.slice(-4);
  const maskedMiddle = "*".repeat(data.length - 8);
  return `${visibleStart}${maskedMiddle}${visibleEnd}`;
};

// Event handlers
const handleToggle = (value: boolean) => {
  emit("toggle", props.paymentGateway.id, value);
};

const handleEdit = () => {
  emit("edit", props.paymentGateway.id);
};
onMounted(() => {
  console.log("props", props.paymentGateway);
});
</script>
