<template>
  <div class="group">
    <!-- Payment Method Row -->
    <div class="flex items-center justify-between p-4 hover:bg-gray-50">
      <!-- Left Side: Toggle + Icon + Name -->
      <div class="flex items-center gap-3">
        <!-- Toggle Switch -->
        <ToggleSwitch
          :model-value="true"
          size="md"
          @change="handleMethodToggle"
        />

        <!-- Method Icon -->
        <div
          class="w-8 h-8 rounded-lg flex items-center justify-center bg-gray-100"
        >
          <img
            v-if="method.image"
            :src="method.image"
            :alt="method.name"
            class="w-6 h-6 object-contain rounded"
          />
          <svg
            v-else
            class="w-6 h-6 text-gray-600"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"
            />
          </svg>
        </div>

        <!-- Method Name -->
        <div>
          <div class="flex items-center gap-2">
            <span class="text-sm font-medium text-gray-900">
              {{ method.name }}
            </span>
            <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
              {{ method.code }}
            </span>
          </div>
          <p class="text-xs text-gray-500">{{ method.description }}</p>
        </div>
      </div>

      <!-- Right Side: Actions -->
      <div class="flex items-center gap-3">
        <!-- Expand/Collapse Button -->
        <button
          @click="toggleExpanded"
          class="p-1 text-gray-400 hover:text-gray-600"
        >
          <svg
            class="w-4 h-4 transition-transform duration-200"
            :class="{ 'rotate-90': isExpanded }"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 5l7 7-7 7"
            />
          </svg>
        </button>

        <!-- More Actions -->
        <button class="p-1 text-gray-400 hover:text-gray-600">
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path
              d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- Expanded Titles (Beneficiary Accounts) -->
    <div
      v-if="isExpanded && dataPaymentGateway.length > 0"
      class="bg-gray-50 border-t border-gray-200"
    >
      <PaymentMethodTitle
        v-for="paymentGateway in dataPaymentGateway"
        :key="paymentGateway.id"
        :paymentGateway="paymentGateway"
        :method-id="method.id"
        @toggle="handleTitleToggle"
        @edit="handleTitleEdit"
      />
    </div>

    <!-- No Titles Message -->
    <div
      v-else-if="isExpanded && (!method.titles || method.titles.length === 0)"
      class="bg-gray-50 border-t border-gray-200 px-4 py-3 ml-14"
    >
      <p class="text-xs text-gray-500 italic">Không có tài khoản thụ hưởng</p>
    </div>
  </div>
</template>

<script setup lang="ts">
interface BeneficiaryTitle {
  __typename?: string;
  id: string;
  code?: string;
  name: string;
  lang?: string;
  showField?: boolean;
  required?: boolean;
  accountNumber?: string;
  accountName?: string;
  bankName?: string;
  bankCode?: string;
  isActive?: boolean;
  isDefault?: boolean;
}

interface ApiPaymentMethod {
  code: string;
  description: string;
  id: string;
  image: string | null;
  name: string;
  titles: BeneficiaryTitle[];
}

interface Props {
  method: ApiPaymentMethod;
  isExpanded: boolean;
}

interface Emits {
  (e: "toggle-expanded"): void;
  (e: "method-toggle", value: boolean): void;
  (e: "title-toggle", titleId: string, value: boolean): void;
  (e: "title-edit", titleId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const toggleExpanded = () => {
  emit("toggle-expanded");
};

const handleMethodToggle = (value: boolean) => {
  emit("method-toggle", value);
};

const handleTitleToggle = (titleId: string, value: boolean) => {
  emit("title-toggle", titleId, value);
};

const handleTitleEdit = (titleId: string) => {
  emit("title-edit", titleId);
};
const { getPaymentGatewaysByMethodCode } = usePayment();
const dataPaymentGateway = ref<any>([]);
const handleGetPaymentGateWwayByMethodCode = async (code: string) => {
  try {
    const response = await getPaymentGatewaysByMethodCode(code);
    dataPaymentGateway.value = response;
    console.log('response',response)
  } catch (error) {
    throw error;
  }
};
onMounted(async () => {
  await handleGetPaymentGateWwayByMethodCode(props.method.code);
});
</script>
