import { ref } from 'vue';

// Global state để quản lý dropdown
const activeDropdownId = ref<string | null>(null);

export const useDropdownManager = () => {
  const openDropdown = (dropdownId: string) => {
    activeDropdownId.value = dropdownId;
  };

  const closeDropdown = () => {
    activeDropdownId.value = null;
  };

  const isDropdownOpen = (dropdownId: string) => {
    return activeDropdownId.value === dropdownId;
  };

  const toggleDropdown = (dropdownId: string) => {
    if (activeDropdownId.value === dropdownId) {
      closeDropdown();
    } else {
      openDropdown(dropdownId);
    }
  };

  return {
    activeDropdownId: readonly(activeDropdownId),
    openDropdown,
    closeDropdown,
    isDropdownOpen,
    toggleDropdown,
  };
};
