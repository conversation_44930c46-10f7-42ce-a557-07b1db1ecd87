<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div>
      <h2 class="text-lg font-medium text-gray-900">
        <PERSON><PERSON><PERSON> <PERSON>ình phương thức thanh toán
      </h2>
      <p class="mt-1 text-sm text-gray-600">
        <PERSON><PERSON><PERSON><PERSON> lý các phư<PERSON>ng thức thanh toán và tài khoản thụ hưởng
      </p>
    </div>
    <!-- Payment Method List (Toggle Style) -->
    <div class="bg-white rounded border border-gray-200">
      <div
        class="flex items-center justify-between p-4 border-b border-gray-200"
      >
        <h3 class="text-base font-medium text-gray-900">
          Danh sách phương thức thanh toán
        </h3>
      </div>

      <!-- Payment Methods List -->
      <div class="divide-y divide-gray-200">
        <!-- API Payment Methods -->
        <ApiPaymentMethodItem
          v-for="method in apiPaymentMethods"
          :key="method.id"
          :method="method"
          :is-expanded="expandedApiMethods.includes(method.id)"
          @toggle-expanded="toggleApiMethod(method.id)"
          @method-toggle="handleApiMethodToggle(method, $event)"
          @title-toggle="
            (titleId, value) => handleTitleToggle(method.id, titleId, value)
          "
          @title-edit="editApiTitle(method.id, $event)"
          @create-gateway="handleGetPaymentMethods"
        />
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Hủy
      </button>
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Lưu thay đổi
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
// Interfaces

interface ApiPaymentMethod {
  code: string;
  description: string;
  id: string;
  image: string | null;
  name: string;
  titles: BeneficiaryTitle[];
}

interface BeneficiaryTitle {
  __typename?: string;
  id: string;
  code?: string;
  name: string;
  lang?: string;
  showField?: boolean;
  required?: boolean;
  accountNumber?: string;
  accountName?: string;
  bankName?: string;
  bankCode?: string;
  isActive?: boolean;
  isDefault?: boolean;
}

// Reactive data

// API Payment Methods data
const apiPaymentMethods = ref<ApiPaymentMethod[]>([]);
const isLoadingPaymentMethods = ref(false);
const expandedApiMethods = ref<string[]>([]);

// Methods

// API Payment Methods handlers
const toggleApiMethod = (methodId: string) => {
  const index = expandedApiMethods.value.indexOf(methodId);
  if (index > -1) {
    expandedApiMethods.value.splice(index, 1);
  } else {
    expandedApiMethods.value.push(methodId);
  }
};

const handleApiMethodToggle = (method: ApiPaymentMethod, value: boolean) => {};

const handleTitleToggle = (
  methodId: string,
  titleId: string,
  value: boolean
) => {
  const method = apiPaymentMethods.value.find((m) => m.id === methodId);
  const title = method?.titles?.find((t) => t.id === titleId);
  if (title) {
    title.isActive = value;
  }
};

const editApiTitle = (methodId: string, titleId: string) => {
  const method = apiPaymentMethods.value.find((m) => m.id === methodId);
  const title = method?.titles?.find((t) => t.id === titleId);
};

//
const { paymentMethods } = usePayment();
const handleGetPaymentMethods = async () => {
  try {
    isLoadingPaymentMethods.value = true;
    const response = await paymentMethods();

    apiPaymentMethods.value = response || [];

    isLoadingPaymentMethods.value = false;
  } catch (error) {
    isLoadingPaymentMethods.value = false;
    throw error;
  }
};

onMounted(async () => {
  await handleGetPaymentMethods();
});
</script>
