<template>
  <!-- Create Gateway Popup -->
  <Teleport to="body">
    <Transition name="fade">
      <div
        v-if="isOpen"
        class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50"
        @click.self="closePopup"
      >
        <Transition name="scale">
          <div
            v-if="isOpen"
            class="bg-white w-full max-w-md p-6 rounded-lg shadow-lg max-h-[80vh] overflow-y-auto"
          >
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-lg font-semibold">Tạo cổng thanh toán</h2>
              <button
                @click="closePopup"
                class="text-gray-500 hover:text-gray-700"
              >
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                  />
                </svg>
              </button>
            </div>
            <div v-for="item in method.titles" :key="item.id">
              {{ item?.code }}
            </div>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
interface Props {
  isOpen: boolean;
  method: any;
}

interface Emits {
  (e: "close"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const closePopup = () => {
  emit("close");
};
const { getPaymentMethodTitles } = usePayment();
const handleGetPaymentMethodTitles = async (methodCode: string) => {
  try {
    const response = await getPaymentMethodTitles(methodCode);
    return response;
  } catch (error) {
    throw error;
  }
};
onMounted(() => {
  console.log("props", props.method);
  props.method.titles?.forEach(async (item: any) => {
    console.log("item", item);
    // if(){

    // }
  });
});
</script>

<style scoped>
/* Fade transition for modal backdrop */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Scale transition for modal content */
.scale-enter-active,
.scale-leave-active {
  transition: all 0.3s ease;
}

.scale-enter-from,
.scale-leave-to {
  opacity: 0;
  transform: scale(0.9);
}
</style>
