<template>
  <!-- Create Gateway Popup -->
  <Teleport to="body">
    <Transition name="fade">
      <div
        class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50"
        @click.self="closePopup"
      >
        <Transition name="scale">
          <div
            v-if="isOpen"
            class="bg-white w-full max-w-2xl p-6 rounded-lg shadow-lg max-h-[80vh] overflow-y-auto"
          >
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-lg font-semibold">Tạo cổng thanh toán</h2>
              <button
                @click="closePopup"
                class="text-gray-500 hover:text-gray-700"
              >
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                  />
                </svg>
              </button>
            </div>
            <!-- Form Fields based on titles -->
            <form @submit.prevent="handleSubmit" class="space-y-4">
              <div
                v-for="title in method.titles"
                :key="title.id"
                v-show="title.showField && title.code"
                class="space-y-2"
              >
                <label
                  :for="title.code"
                  class="block text-sm font-medium text-gray-700"
                >
                  {{ title.name }}
                  <span v-if="title.required" class="text-red-500">*</span>
                </label>
                <input
                  :id="title.code"
                  v-model="formData[title.code!]"
                  :required="title.required"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary outline-none transition-colors"
                  :placeholder="`Nhập ${title.name.toLowerCase()}`"
                />
              </div>

              <!-- Action Buttons -->
              <div class="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  @click="closePopup"
                  class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Hủy
                </button>
                <button
                  type="submit"
                  class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
                >
                  Tạo cổng thanh toán
                </button>
              </div>
            </form>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
interface BeneficiaryTitle {
  __typename?: string;
  id: string;
  code?: string;
  name: string;
  lang?: string;
  showField?: boolean;
  required?: boolean;
  accountNumber?: string;
  accountName?: string;
  bankName?: string;
  bankCode?: string;
  isActive?: boolean;
  isDefault?: boolean;
}

interface ApiPaymentMethod {
  code: string;
  description: string;
  id: string;
  image: string | null;
  name: string;
  titles: BeneficiaryTitle[];
}

interface Props {
  isOpen: boolean;
  method: ApiPaymentMethod;
}

interface Emits {
  (e: "close"): void;
  (e: "submit", formData: Record<string, string>): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Form data reactive object
const formData = ref<Record<string, string>>({});

// Initialize form data when component mounts or method changes
watch(
  () => props.method,
  (newMethod) => {
    if (newMethod?.titles) {
      const initialData: Record<string, string> = {};
      newMethod.titles.forEach((title) => {
        if (title.showField && title.code) {
          initialData[title.code] = "";
        }
      });
      formData.value = initialData;
    }
  },
  { immediate: true }
);

const closePopup = () => {
  emit("close");
};

const handleSubmit = () => {
  console.log("Form submitted with data:", formData.value);
  emit("submit", formData.value);
  closePopup();
};
</script>

<style scoped>
/* Fade transition for modal backdrop */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Scale transition for modal content */
.scale-enter-active,
.scale-leave-active {
  transition: all 0.3s ease;
}

.scale-enter-from,
.scale-leave-to {
  opacity: 0;
  transform: scale(0.9);
}
</style>
