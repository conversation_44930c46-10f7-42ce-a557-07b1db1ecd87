<template>
  <!-- <PERSON>reate Gateway Popup -->
  <Teleport to="body">
    <Transition name="fade">
      <div
        class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50"
        @click.self="closePopup"
      >
        <Transition name="scale">
          <div
            class="bg-gray-100 w-full max-w-2xl rounded-lg shadow-lg max-h-[80vh] overflow-hidden"
          >
            <!-- Header -->
            <div
              class="bg-primary text-white px-4 py-2.5 rounded-t-lg flex items-center justify-between"
            >
              <div class="flex items-center gap-2.5">
                <div
                  class="flex items-center justify-center w-7 h-7 bg-white/20 rounded-full"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="2"
                    stroke="currentColor"
                    class="w-4 h-4"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"
                    />
                  </svg>
                </div>
                <div>
                  <h2 class="text-lg font-semibold">Tạo cổng thanh toán</h2>
                  <p class="text-white/75 text-xs">
                    {{ method?.name || "Đang tải..." }}
                  </p>
                </div>
              </div>

              <div class="flex items-center gap-2">
                <button
                  @click="closePopup"
                  class="flex items-center justify-center bg-white/20 p-1.5 hover:bg-white/30 rounded-full transition-colors duration-200"
                  title="Đóng popup"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="2"
                    stroke="currentColor"
                    class="w-4 h-4"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
            </div>
            <!-- Content -->
            <div
              class="bg-white p-4 overflow-y-auto"
              style="max-height: calc(80vh - 100px)"
            >
              <!-- Form Fields based on titles -->
              <form @submit.prevent="handleSubmit" class="space-y-4">
                <div
                  v-for="title in method.titles"
                  :key="title.id"
                  v-show="title.showField && title.code"
                  class="space-y-2"
                >
                  <label
                    :for="title.code"
                    class="block text-sm font-medium text-gray-700"
                  >
                    {{ title?.name }}
                    <span v-if="title.required" class="text-red-500">*</span>
                  </label>

                  <!-- Select dropdown for publicKey -->
                  <select
                    v-if="title.code === 'publicKey'"
                    :id="title.code"
                    v-model="formData[title.code!]"
                    :required="title.required"
                    class="w-full px-3 py-2 border border-gray-300 rounded outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                  >
                    <option value="">Chọn ngân hàng</option>
                    <option
                      v-for="bank in bankOptions"
                      :key="bank.code"
                      :value="bank.code"
                    >
                      {{ bank.name }}
                    </option>
                  </select>

                  <!-- Select dropdown for paymentRecordingMethod -->
                  <select
                    v-else-if="title.code === 'submethod'"
                    :id="title.code"
                    v-model="formData[title.code!]"
                    :required="title.required"
                    class="w-full px-3 py-2 border border-gray-300 rounded outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                  >
                    <option value="">Chọn phương thức</option>
                    <option value="PAY">PAY</option>
                    <option value="REFUND">REFUND</option>
                    <option value="QUERY">QUERY</option>
                    <option value="COMPLETE">COMPLETE</option>
                  </select>

                  <!-- Regular input for other fields -->
                  <input
                    v-else
                    :id="title.code"
                    v-model="formData[title.code!]"
                    :required="title.required"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                    :placeholder="`Nhập ${title.name.toLowerCase()}`"
                  />
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    @click="closePopup"
                    class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Hủy
                  </button>
                  <button
                    type="submit"
                    class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
                  >
                    Tạo cổng thanh toán
                  </button>
                </div>
              </form>
            </div>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
interface BeneficiaryTitle {
  __typename?: string;
  id: string;
  code?: string;
  name: string;
  lang?: string;
  showField?: boolean;
  required?: boolean;
  accountNumber?: string;
  accountName?: string;
  bankName?: string;
  bankCode?: string;
  isActive?: boolean;
  isDefault?: boolean;
}

interface ApiPaymentMethod {
  code: string;
  description: string;
  id: string;
  image: string | null;
  name: string;
  titles: BeneficiaryTitle[];
}

interface Props {
  method: ApiPaymentMethod;
}

interface Emits {
  (e: "close"): void;
  (e: "submit", formData: Record<string, string>): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Form data reactive object
const formData = ref<Record<string, string>>({});

// Initialize form data when component mounts or method changes
watch(
  () => props.method,
  (newMethod) => {
    if (newMethod?.titles) {
      const initialData: Record<string, string> = {};
      newMethod.titles.forEach((title) => {
        if (title.showField && title.code) {
          // Pre-fill "method" field with method code
          if (title.code === "method") {
            initialData[title.code] = newMethod.code || "";
          } else {
            initialData[title.code] = "";
          }
        }
      });
      formData.value = initialData;
    }
  },
  { immediate: true }
);

// Watch for publicKey changes to auto-fill accessKey
watch(
  () => formData.value.publicKey,
  (newBankCode) => {
    if (newBankCode && formData.value.hasOwnProperty("accessKey")) {
      // Auto-fill accessKey with the selected bank code
      formData.value.accessKey = newBankCode;
    }
  }
);

const closePopup = () => {
  emit("close");
};

// Bank options for publicKey select
const { getBanks } = useVietQr();
const bankOptions = ref<Array<{ code: string; name: string }>>([]);

// Load banks when component mounts
onMounted(async () => {
  try {
    const banks = await getBanks();
    bankOptions.value = banks?.data || [];
  } catch (error) {
    throw error;
  }
});

const handleSubmit = () => {
  emit("submit", formData.value);
};
</script>

<style scoped>
/* Fade transition for modal backdrop */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Scale transition for modal content */
.scale-enter-active,
.scale-leave-active {
  transition: all 0.3s ease;
}

.scale-enter-from,
.scale-leave-to {
  opacity: 0;
  transform: scale(0.9);
}
</style>
