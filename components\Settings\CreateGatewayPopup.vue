<template>
  <!-- Create Gateway Popup -->
  <Teleport to="body">
    <Transition name="fade">
      <div
        class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50"
        @click.self="closePopup"
      >
        <Transition name="scale">
          <div
            v-if="isOpen"
            class="bg-gray-100 w-full max-w-2xl rounded-lg shadow-lg max-h-[80vh] overflow-hidden"
          >
            <!-- Header -->
            <div
              class="bg-primary text-white px-4 py-3 rounded-t-lg flex items-center justify-between -m-6 mb-6"
            >
              <div class="flex items-center gap-3">
                <div
                  class="flex items-center justify-center w-8 h-8 bg-white/20 rounded-full"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="2"
                    stroke="currentColor"
                    class="w-5 h-5"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"
                    />
                  </svg>
                </div>
                <div>
                  <h2 class="text-xl font-bold">Tạo cổng thanh toán</h2>
                  <p class="text-white/80 text-sm">
                    {{ method?.name || "Đang tải..." }}
                  </p>
                </div>
              </div>

              <div class="flex items-center gap-3">
                <!-- Method Code Badge -->
                <div
                  v-if="method?.code"
                  class="px-3 py-1 bg-white/20 rounded-full"
                >
                  <span class="text-sm font-medium">
                    {{ method.code }}
                  </span>
                </div>

                <button
                  @click="closePopup"
                  class="flex items-center justify-center bg-white/20 p-2 hover:bg-white/30 rounded transition-colors duration-200"
                  title="Đóng popup"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="2"
                    stroke="currentColor"
                    class="w-5 h-5"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
            </div>
            <!-- Content -->
            <div
              class="bg-white p-6 overflow-y-auto"
              style="max-height: calc(80vh - 120px)"
            >
              <!-- Form Fields based on titles -->
              <form @submit.prevent="handleSubmit" class="space-y-4">
                <div
                  v-for="title in method.titles"
                  :key="title.id"
                  v-show="title.showField && title.code"
                  class="space-y-2"
                >
                  <label
                    :for="title.code"
                    class="block text-sm font-medium text-gray-700"
                  >
                    {{ title.name }}
                    <span v-if="title.required" class="text-red-500">*</span>
                  </label>
                  <input
                    :id="title.code"
                    v-model="formData[title.code!]"
                    :required="title.required"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary outline-none transition-colors"
                    :placeholder="`Nhập ${title.name.toLowerCase()}`"
                  />
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    @click="closePopup"
                    class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Hủy
                  </button>
                  <button
                    type="submit"
                    class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
                  >
                    Tạo cổng thanh toán
                  </button>
                </div>
              </form>
            </div>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
interface BeneficiaryTitle {
  __typename?: string;
  id: string;
  code?: string;
  name: string;
  lang?: string;
  showField?: boolean;
  required?: boolean;
  accountNumber?: string;
  accountName?: string;
  bankName?: string;
  bankCode?: string;
  isActive?: boolean;
  isDefault?: boolean;
}

interface ApiPaymentMethod {
  code: string;
  description: string;
  id: string;
  image: string | null;
  name: string;
  titles: BeneficiaryTitle[];
}

interface Props {
  isOpen: boolean;
  method: ApiPaymentMethod;
}

interface Emits {
  (e: "close"): void;
  (e: "submit", formData: Record<string, string>): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Form data reactive object
const formData = ref<Record<string, string>>({});

// Initialize form data when component mounts or method changes
watch(
  () => props.method,
  (newMethod) => {
    if (newMethod?.titles) {
      const initialData: Record<string, string> = {};
      newMethod.titles.forEach((title) => {
        if (title.showField && title.code) {
          initialData[title.code] = "";
        }
      });
      formData.value = initialData;
    }
  },
  { immediate: true }
);

const closePopup = () => {
  emit("close");
};

const handleSubmit = () => {
  console.log("Form submitted with data:", formData.value);
  emit("submit", formData.value);
  closePopup();
};
</script>

<style scoped>
/* Fade transition for modal backdrop */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Scale transition for modal content */
.scale-enter-active,
.scale-leave-active {
  transition: all 0.3s ease;
}

.scale-enter-from,
.scale-leave-to {
  opacity: 0;
  transform: scale(0.9);
}
</style>
