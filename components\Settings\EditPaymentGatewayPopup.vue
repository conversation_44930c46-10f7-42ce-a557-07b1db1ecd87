<template>
  <!-- Edit Payment Gateway Popup -->
  <Teleport to="body">
    <Transition name="fade">
      <div
        v-if="isOpen"
        class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50"
        @click.self="closePopup"
      >
        <Transition name="scale">
          <div
            class="bg-gray-100 w-full max-w-2xl rounded-lg shadow-lg max-h-[80vh] overflow-hidden"
          >
            <!-- Header -->
            <div
              class="bg-primary text-white px-4 py-2.5 rounded-t-lg flex items-center justify-between"
            >
              <div class="flex items-center gap-2.5">
                <div
                  class="flex items-center justify-center w-7 h-7 bg-white/20 rounded-full"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="2"
                    stroke="currentColor"
                    class="w-4 h-4"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                    />
                  </svg>
                </div>
                <div>
                  <h2 class="text-lg font-semibold">Chỉnh sửa cổng thanh toán</h2>
                  <p class="text-white/75 text-xs">
                    {{ paymentGateway?.name || "Đang tải..." }}
                  </p>
                </div>
              </div>

              <button
                @click="closePopup"
                class="flex items-center justify-center bg-white/20 p-1.5 hover:bg-white/30 rounded-full transition-colors duration-200"
                title="Đóng popup"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="2"
                  stroke="currentColor"
                  class="w-4 h-4"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            <!-- Content -->
            <div
              class="bg-white p-4 overflow-y-auto"
              style="max-height: calc(80vh - 100px)"
            >
              <!-- Form Fields -->
              <form @submit.prevent="handleSubmit" class="space-y-4">
                <!-- Gateway Name -->
                <div class="space-y-2">
                  <label class="block text-sm font-medium text-gray-700">
                    Tên cổng thanh toán
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    v-model="formData.name"
                    required
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                    placeholder="Nhập tên cổng thanh toán"
                  />
                </div>

                <!-- Partner Code -->
                <div class="space-y-2">
                  <label class="block text-sm font-medium text-gray-700">
                    Partner Code
                  </label>
                  <input
                    v-model="formData.partnerCode"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                    placeholder="Nhập partner code"
                  />
                </div>

                <!-- Sub Method Code -->
                <div class="space-y-2">
                  <label class="block text-sm font-medium text-gray-700">
                    Sub Method Code
                  </label>
                  <select
                    v-model="formData.subMethodCode"
                    class="w-full px-3 py-2 border border-gray-300 rounded outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                  >
                    <option value="">Chọn phương thức</option>
                    <option value="PAY">PAY</option>
                    <option value="REFUND">REFUND</option>
                    <option value="QUERY">QUERY</option>
                    <option value="COMPLETE">COMPLETE</option>
                  </select>
                </div>

                <!-- Gateway Partner Code -->
                <div class="space-y-2">
                  <label class="block text-sm font-medium text-gray-700">
                    Gateway Partner Code
                  </label>
                  <input
                    v-model="formData.gwPartnerCode"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                    placeholder="Nhập gateway partner code"
                  />
                </div>

                <!-- Gateway Partner Name -->
                <div class="space-y-2">
                  <label class="block text-sm font-medium text-gray-700">
                    Gateway Partner Name
                  </label>
                  <input
                    v-model="formData.gwPartnerName"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                    placeholder="Nhập gateway partner name"
                  />
                </div>

                <!-- Access Key -->
                <div class="space-y-2">
                  <label class="block text-sm font-medium text-gray-700">
                    Access Key
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    v-model="formData.accessKey"
                    required
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                    placeholder="Nhập access key"
                  />
                </div>

                <!-- Secret Key -->
                <div class="space-y-2">
                  <label class="block text-sm font-medium text-gray-700">
                    Secret Key
                  </label>
                  <input
                    v-model="formData.secretKey"
                    type="password"
                    class="w-full px-3 py-2 border border-gray-300 rounded outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                    placeholder="Nhập secret key"
                  />
                </div>

                <!-- Request URL -->
                <div class="space-y-2">
                  <label class="block text-sm font-medium text-gray-700">
                    Request URL
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    v-model="formData.requestUrl"
                    required
                    type="url"
                    class="w-full px-3 py-2 border border-gray-300 rounded outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                    placeholder="Nhập request URL"
                  />
                </div>

                <!-- Description -->
                <div class="space-y-2">
                  <label class="block text-sm font-medium text-gray-700">
                    Mô tả
                  </label>
                  <textarea
                    v-model="formData.description"
                    rows="3"
                    class="w-full px-3 py-2 border border-gray-300 rounded outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                    placeholder="Nhập mô tả"
                  ></textarea>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    @click="closePopup"
                    class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Hủy
                  </button>
                  <button
                    type="submit"
                    class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
                  >
                    Cập nhật
                  </button>
                </div>
              </form>
            </div>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
interface PaymentGateway {
  __typename?: string;
  id: string;
  name: string;
  methodCode: string;
  partnerCode: string;
  subMethodCode: string;
  gwPartnerCode: string;
  gwPartnerName?: string;
  gwSubChannel?: string;
  gwMethodVersion?: string;
  hashAlgorithm?: string;
  accessKey: string;
  secretKey?: string;
  requestUrl: string;
  description: string;
  activated: boolean;
}

interface Props {
  isOpen: boolean;
  paymentGateway: PaymentGateway;
}

interface Emits {
  (e: "close"): void;
  (e: "submit", formData: PaymentGateway): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Form data reactive object
const formData = ref<PaymentGateway>({
  id: '',
  name: '',
  methodCode: '',
  partnerCode: '',
  subMethodCode: '',
  gwPartnerCode: '',
  gwPartnerName: '',
  accessKey: '',
  secretKey: '',
  requestUrl: '',
  description: '',
  activated: true
});

// Initialize form data when paymentGateway changes
watch(
  () => props.paymentGateway,
  (newGateway) => {
    if (newGateway) {
      formData.value = { ...newGateway };
    }
  },
  { immediate: true }
);

const closePopup = () => {
  emit("close");
};

const handleSubmit = () => {
  emit("submit", formData.value);
  closePopup();
};
</script>

<style scoped>
/* Fade transition for modal backdrop */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Scale transition for modal content */
.scale-enter-active,
.scale-leave-active {
  transition: all 0.3s ease;
}

.scale-enter-from,
.scale-leave-to {
  opacity: 0;
  transform: scale(0.9);
}
</style>
