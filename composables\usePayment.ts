export default function usePayment() {
  const $sdk = useNuxtApp().$sdk;
  const dataPaymentMethod = ref([]);
  const isLoading = ref<Boolean>(false);

  const createPaymentOrder = async (requestData: any) => {
    try {
      const response = await $sdk.payment.createPaymentOrder(requestData);
      return response;
    } catch (error) {
      console.error("Error create payment order:", error);
      useNuxtApp().$toast.error(
        `${error}!\nTạo thanh toán lỗi vui lòng tạo lại`
      );
    }
  };
  const getPaymentMethods = async () => {
    try {
      isLoading.value = true;
      const response = await $sdk.payment.getPaymentMethod();
      dataPaymentMethod.value = response;
      isLoading.value = false;
      return response;
    } catch (error) {
      throw error;
    }
  };
  const genQrPayment = async (orderId: any, totalAmount: any) => {
    try {
      const response = await $sdk.payment.genQRPayment(orderId, totalAmount);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getOrderById = async (orderId: string) => {
    try {
      const response = await $sdk.order.getInfoSellOrder(orderId);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getInvoiceDetail = async (invoiceId: string) => {
    try {
      const response = await $sdk.payment.getInvoiceDetail(invoiceId);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const cancelPayment = async (paymentId: string, reason: string) => {
    try {
      const response = await $sdk.paymentV2.cancelPayment(paymentId, reason);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const paymentsByOrders = async (orderIds: [string]) => {
    try {
      const response = await $sdk.paymentV2.paymentsByOrders(orderIds);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const paymentMethods = async () => {
    try {
      const response = await $sdk.paymentV2.paymentMethods();
      dataPaymentMethod.value = response;
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getPaymentMethodTypes = async () => {
    try {
      const response = await $sdk.paymentV2.getPaymentMethodTypes();
      dataPaymentMethod.value = response;
      return response;
    } catch (error) {
      throw error;
    }
  };

  const getAndroidBank = async () => {
    try {
      const response = await $sdk.deepLinkVietQr.getAndroidBank();
      return response.apps;
    } catch (error) {
      throw error;
    }
  };
  const getIosBank = async () => {
    try {
      const response = await $sdk.deepLinkVietQr.getIosBank();
      return response.apps;
    } catch (error) {
      throw error;
    }
  };
  /// payment for payment id
  const paymentInfo = async (paymentId: string) => {
    try {
      const response = await $sdk.paymentV2.paymentInfo(paymentId);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const confirmToGateway = async (
    paymentId: string,
    methodCode: string,
    returnUrl: string
  ) => {
    try {
      const response = await $sdk.paymentV2.confirmToGateway(
        paymentId,
        methodCode,
        returnUrl
      );
      if (response?.code !== "0") {
        useNuxtApp().$toast.warning(response?.message);
      } else {
        useNuxtApp().$toast.success(response?.message);
      }
      return response;
    } catch (error: any) {
      useNuxtApp().$toast.warning(`${error?.message}`);
      throw error;
    }
  };
  const paymentStatus = async (paymentId: string) => {
    try {
      const response = await $sdk.paymentV2.paymentStatus(paymentId);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getConfigPayment = async (configId: string) => {
    try {
      const response = await $sdk.paymentV2.gwConfigDetail(configId);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const confirmPayment = async (
    paymentId: string,
    methodCode: string,
    note: string,
    confirmBy: string
  ) => {
    try {
      const response = await $sdk.paymentV2.confirmPaymentSuccessManual(
        paymentId,
        methodCode,
        note,
        confirmBy
      );
      useNuxtApp().$toast.success(response?.message);

      return response;
    } catch (error: any) {
      useNuxtApp().$toast.error(error?.message);
      throw error;
    }
  };
  const getPaymentMethodTitles = async (methodCode: string) => {
    try {
      const response = await $sdk.paymentV2.getPaymentMethodTitles(methodCode);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const paymentMethod = async () => {
    try {
      const response = await $sdk.paymentV2.paymentMethods();
      return response;
    } catch (error) {
      throw error;
    }
  };
  const handleCreateGatewayConfig = async (input: any, cassoApiKey: string) => {
    try {
      const response = await $sdk.paymentV2.handleCreateGatewayConfig(
        input,
        cassoApiKey
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const handleUpdateGatewayConfig = async (
    methodCode: String,
    subMethodCode: string,
    gwPartnerCode: string,
    gatewayId: string,
    cassoApiKey: string
  ) => {
    try {
      const data = {
        methodCode,
        subMethodCode,
        gwPartnerCode,
        gatewayId,
      };
      const response = await $sdk.paymentV2.handleUpdateGatewayConfig(
        data,
        cassoApiKey
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getPaymentGatewaysByMethodCode = async (methodCode: string) => {
    try {
      const response = await $sdk.paymentV2.getPaymentGatewaysByMethodCode(
        methodCode
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  return {
    createPaymentOrder,
    getPaymentMethods,
    dataPaymentMethod,
    genQrPayment,
    getOrderById,
    getInvoiceDetail,
    isLoading,
    cancelPayment,
    paymentsByOrders,
    paymentMethods,
    getPaymentMethodTypes,
    getAndroidBank,
    getIosBank,
    paymentInfo,
    confirmToGateway,
    paymentStatus,
    getConfigPayment,
    confirmPayment,
    getPaymentMethodTitles,
    paymentMethod,
    handleCreateGatewayConfig,
    handleUpdateGatewayConfig,
    getPaymentGatewaysByMethodCode,
  };
}
